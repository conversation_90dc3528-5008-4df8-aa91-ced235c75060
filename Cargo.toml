[package]
authors = ["<PERSON> <<EMAIL>>"]
edition = "2018"
name = "mini-redis"
version = "0.4.1"
license = "MIT"
readme = "README.md"
documentation = "https://docs.rs/mini-redis/0.4.0/mini-redis/"
repository = "https://github.com/tokio-rs/mini-redis"
description = """
An incomplete implementation of a Rust client and server. Used as a
larger example of an idiomatic Tokio application.
"""

[[bin]]
name = "mini-redis-cli"
path = "src/bin/cli.rs"

[[bin]]
name = "mini-redis-server"
path = "src/bin/server.rs"

[[bin]]
name = "mini-redis-server-optimized"
path = "src/bin/server_optimized.rs"

[dependencies]
async-stream = "0.3.0"
atoi = "2.0.0"
bytes = "1"
clap = { version = "4.2.7", features = ["derive"] }
tokio = { version = "1", features = ["full"] }
tokio-stream = "0.1"
tracing = "0.1.34"
tracing-subscriber = { version = "0.3.11", features = ["env-filter"] }
# Implements the types defined in the OTel spec
opentelemetry = { version = "0.20.0", optional = true }
# Integration between the tracing crate and the opentelemetry crate
tracing-opentelemetry = { version = "0.21.0", optional = true }
# Provides a "propagator" to pass along an XrayId across services
opentelemetry-aws = { version = "0.8.0", optional = true }
# Allows you to send data to the OTel collector
opentelemetry-otlp = { version = "0.13.0", optional = true }

# Thread pool management
num_cpus = "1.0"
thread_local = "1.1"

# CPU affinity (optional, Linux only)
[target.'cfg(target_os = "linux")'.dependencies]
core-affinity = "0.8"

[dev-dependencies]
# Enable test-utilities in dev mode only. This is mostly for tests.
tokio = { version = "1", features = ["test-util"] }

[features]
otel = ["dep:opentelemetry", "dep:tracing-opentelemetry", "dep:opentelemetry-aws", "dep:opentelemetry-otlp"]
