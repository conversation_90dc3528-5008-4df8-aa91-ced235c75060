name: CI

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Build
      run: cargo build --verbose
    - name: Build with OTel feature
      run: cargo build --verbose --features otel
    - name: Run tests
      run: cargo test --verbose
    - name: Run tests with OTel feature
      run: cargo test --verbose --features otel
    - name: rustfmt
      run: cargo fmt --all --check
