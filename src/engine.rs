//! Redis-like architecture with IO threads and single logic thread
//!
//! This module implements a new architecture where:
//! - Multiple IO threads handle network connections and protocol parsing
//! - A single logic thread handles all data operations (no locks needed)
//! - Communication happens via message passing

use crate::{Command, Frame};
use bytes::Bytes;
use std::collections::HashMap;
use tokio::sync::{broadcast, mpsc, oneshot};
use tracing::{debug, error, info};

/// Messages sent from IO threads to the logic thread
#[derive(Debug)]
pub enum LogicMessage {
    /// Execute a command and send response back
    Command {
        cmd: Command,
        response_tx: oneshot::Sender<Frame>,
    },
    /// Subscribe to a channel
    Subscribe {
        channel: String,
        subscriber_tx: mpsc::Sender<Bytes>,
        response_tx: oneshot::Sender<Frame>,
    },
    /// Unsubscribe from a channel
    Unsubscribe {
        channel: String,
        subscriber_id: u64,
        response_tx: oneshot::Sender<Frame>,
    },
    /// Publish to a channel
    Publish {
        channel: String,
        message: Bytes,
        response_tx: oneshot::Sender<Frame>,
    },
    /// Shutdown the logic thread
    Shutdown,
}

/// Messages sent from logic thread to IO threads (for pub/sub)
#[derive(Debug, Clone)]
pub enum IoMessage {
    /// A message published to a channel
    Published {
        channel: String,
        message: Bytes,
    },
}

/// Entry in the key-value store
#[derive(Debug, Clone)]
struct Entry {
    data: Bytes,
    expires_at: Option<std::time::Instant>,
}

/// Subscriber information for pub/sub
#[derive(Debug)]
struct Subscriber {
    id: u64,
    tx: mpsc::Sender<Bytes>,
}

/// The single-threaded logic engine
pub struct LogicEngine {
    /// Key-value storage (no mutex needed - single threaded)
    kv_store: HashMap<String, Entry>,
    
    /// Pub/sub channels and their subscribers
    pub_sub: HashMap<String, Vec<Subscriber>>,
    
    /// Next subscriber ID
    next_subscriber_id: u64,
    
    /// Receiver for commands from IO threads
    command_rx: mpsc::Receiver<LogicMessage>,
    
    /// Sender for broadcasting to IO threads
    io_broadcast_tx: broadcast::Sender<IoMessage>,
}

impl LogicEngine {
    /// Create a new logic engine
    pub fn new(
        command_rx: mpsc::Receiver<LogicMessage>,
        io_broadcast_tx: broadcast::Sender<IoMessage>,
    ) -> Self {
        Self {
            kv_store: HashMap::new(),
            pub_sub: HashMap::new(),
            next_subscriber_id: 1,
            command_rx,
            io_broadcast_tx,
        }
    }
    
    /// Run the logic engine (single threaded)
    pub async fn run(mut self) {
        info!("Logic engine started");
        
        while let Some(message) = self.command_rx.recv().await {
            match message {
                LogicMessage::Command { cmd, response_tx } => {
                    let response = self.handle_command(cmd).await;
                    let _ = response_tx.send(response);
                }
                LogicMessage::Subscribe { channel, subscriber_tx, response_tx } => {
                    let response = self.handle_subscribe(channel, subscriber_tx).await;
                    let _ = response_tx.send(response);
                }
                LogicMessage::Unsubscribe { channel, subscriber_id, response_tx } => {
                    let response = self.handle_unsubscribe(channel, subscriber_id).await;
                    let _ = response_tx.send(response);
                }
                LogicMessage::Publish { channel, message, response_tx } => {
                    let response = self.handle_publish(channel, message).await;
                    let _ = response_tx.send(response);
                }
                LogicMessage::Shutdown => {
                    info!("Logic engine shutting down");
                    break;
                }
            }
        }
    }
    
    /// Handle a regular command (GET, SET, etc.)
    async fn handle_command(&mut self, cmd: Command) -> Frame {
        match cmd {
            Command::Get(get) => {
                debug!("GET {}", get.key());
                match self.kv_store.get(get.key()) {
                    Some(entry) => {
                        // Check if expired
                        if let Some(expires_at) = entry.expires_at {
                            if std::time::Instant::now() > expires_at {
                                self.kv_store.remove(get.key());
                                return Frame::Null;
                            }
                        }
                        Frame::Bulk(entry.data.clone())
                    }
                    None => Frame::Null,
                }
            }
            Command::Set(set) => {
                debug!("SET {} = {:?}", set.key(), set.value());
                let expires_at = set.expire().map(|duration| {
                    std::time::Instant::now() + duration
                });
                
                let entry = Entry {
                    data: set.value().clone(),
                    expires_at,
                };
                
                self.kv_store.insert(set.key().to_string(), entry);
                Frame::Simple("OK".to_string())
            }
            Command::Ping(ping) => {
                debug!("PING");
                match ping.msg() {
                    Some(msg) => Frame::Bulk(msg.clone()),
                    None => Frame::Simple("PONG".to_string()),
                }
            }
            _ => {
                error!("Unsupported command in handle_command: {:?}", cmd);
                Frame::Error("ERR unsupported command".to_string())
            }
        }
    }
    
    /// Handle subscribe command
    async fn handle_subscribe(&mut self, channel: String, subscriber_tx: mpsc::Sender<Bytes>) -> Frame {
        debug!("SUBSCRIBE {}", channel);
        
        let subscriber_id = self.next_subscriber_id;
        self.next_subscriber_id += 1;
        
        let subscriber = Subscriber {
            id: subscriber_id,
            tx: subscriber_tx,
        };
        
        self.pub_sub.entry(channel.clone()).or_insert_with(Vec::new).push(subscriber);
        
        // Return subscription confirmation
        let mut response = Frame::Array(vec![]);
        if let Frame::Array(ref mut arr) = response {
            arr.push(Box::new(Frame::Bulk(Bytes::from_static(b"subscribe"))));
            arr.push(Box::new(Frame::Bulk(Bytes::from(channel))));
            arr.push(Box::new(Frame::Integer(subscriber_id)));
        }
        response
    }
    
    /// Handle unsubscribe command
    async fn handle_unsubscribe(&mut self, channel: String, subscriber_id: u64) -> Frame {
        debug!("UNSUBSCRIBE {} (id: {})", channel, subscriber_id);
        
        if let Some(subscribers) = self.pub_sub.get_mut(&channel) {
            subscribers.retain(|sub| sub.id != subscriber_id);
            if subscribers.is_empty() {
                self.pub_sub.remove(&channel);
            }
        }
        
        Frame::Simple("OK".to_string())
    }
    
    /// Handle publish command
    async fn handle_publish(&mut self, channel: String, message: Bytes) -> Frame {
        debug!("PUBLISH {} = {:?}", channel, message);
        
        let mut subscriber_count = 0;
        
        if let Some(subscribers) = self.pub_sub.get_mut(&channel) {
            // Remove disconnected subscribers and send to active ones
            subscribers.retain(|subscriber| {
                match subscriber.tx.try_send(message.clone()) {
                    Ok(_) => {
                        subscriber_count += 1;
                        true // Keep this subscriber
                    }
                    Err(_) => {
                        debug!("Removing disconnected subscriber {}", subscriber.id);
                        false // Remove this subscriber
                    }
                }
            });
            
            // Clean up empty channel
            if subscribers.is_empty() {
                self.pub_sub.remove(&channel);
            }
        }
        
        // Broadcast to IO threads for any connected subscribers
        if subscriber_count > 0 {
            let io_message = IoMessage::Published {
                channel: channel.clone(),
                message: message.clone(),
            };
            let _ = self.io_broadcast_tx.send(io_message);
        }
        
        Frame::Integer(subscriber_count as u64)
    }
}

/// Factory for creating the logic engine and communication channels
pub struct EngineFactory;

impl EngineFactory {
    /// Create a new logic engine with communication channels
    pub fn create() -> (
        LogicEngine,
        mpsc::Sender<LogicMessage>,
        broadcast::Receiver<IoMessage>,
    ) {
        Self::create_with_buffer_sizes(1000, 1000)
    }

    /// Create a new logic engine with custom buffer sizes
    pub fn create_with_buffer_sizes(
        command_buffer_size: usize,
        io_broadcast_buffer_size: usize,
    ) -> (
        LogicEngine,
        mpsc::Sender<LogicMessage>,
        broadcast::Receiver<IoMessage>,
    ) {
        let (command_tx, command_rx) = mpsc::channel(command_buffer_size);
        let (io_broadcast_tx, io_broadcast_rx) = broadcast::channel(io_broadcast_buffer_size);

        let engine = LogicEngine::new(command_rx, io_broadcast_tx);

        (engine, command_tx, io_broadcast_rx)
    }
}
