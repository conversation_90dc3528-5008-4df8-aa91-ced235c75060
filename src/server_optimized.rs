//! Optimized Redis-like server with dedicated thread pools
//!
//! This implementation uses:
//! - Dedicated single thread for logic operations (no contention)
//! - Separate IO thread pool for network operations
//! - Background thread pool for maintenance tasks
//! - CPU affinity for optimal performance

use crate::engine::{EngineFactory, LogicMessage};
use crate::io_handler::<PERSON>oH<PERSON><PERSON>;
use crate::thread_pools::{ThreadPoolConfig, ThreadPoolManager, CpuAffinity};
use crate::{Connection, Shutdown};

use std::future::Future;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::{broadcast, mpsc, Semaphore};
use tokio::time::{self, Duration};
use tracing::{debug, error, info, instrument, warn};

/// Maximum number of concurrent connections
const MAX_CONNECTIONS: usize = 1000;

/// Optimized server configuration
#[derive(Debug, Clone)]
pub struct ServerConfig {
    /// Thread pool configuration
    pub thread_pools: ThreadPoolConfig,
    
    /// Maximum concurrent connections
    pub max_connections: usize,
    
    /// Enable CPU affinity optimization
    pub enable_cpu_affinity: bool,
    
    /// Logic thread channel buffer size
    pub logic_channel_buffer: usize,
    
    /// IO broadcast channel buffer size
    pub io_broadcast_buffer: usize,
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            thread_pools: ThreadPoolConfig::default(),
            max_connections: MAX_CONNECTIONS,
            enable_cpu_affinity: true,
            logic_channel_buffer: 10000,
            io_broadcast_buffer: 1000,
        }
    }
}

/// Optimized server with dedicated thread pools
pub struct OptimizedServer {
    /// Thread pool manager
    thread_pools: Arc<ThreadPoolManager>,
    
    /// Server configuration
    config: ServerConfig,
    
    /// Connection ID counter
    next_connection_id: AtomicU64,
}

impl OptimizedServer {
    /// Create a new optimized server
    pub fn new(config: ServerConfig) -> crate::Result<Self> {
        let thread_pools = Arc::new(ThreadPoolManager::new(config.thread_pools.clone())?);
        
        // Set up CPU affinity if enabled
        if config.enable_cpu_affinity {
            if let Err(e) = Self::setup_cpu_affinity() {
                warn!("Failed to set up CPU affinity: {}", e);
            }
        }
        
        Ok(Self {
            thread_pools,
            config,
            next_connection_id: AtomicU64::new(1),
        })
    }
    
    /// Run the optimized server
    pub async fn run(
        self,
        listener: TcpListener,
        shutdown: impl Future + Send + 'static,
    ) -> crate::Result<()> {
        info!("Starting optimized Redis server");
        
        // Create the logic engine and communication channels
        let (logic_engine, logic_tx, _io_broadcast_rx) = EngineFactory::create_with_buffer_sizes(
            self.config.logic_channel_buffer,
            self.config.io_broadcast_buffer,
        );
        
        // Start the logic thread on dedicated runtime
        let logic_handle = self.thread_pools.spawn_logic(async move {
            info!("Logic thread started on dedicated runtime");
            logic_engine.run().await;
            info!("Logic thread finished");
        });
        
        // Create shutdown channels
        let (notify_shutdown, _) = broadcast::channel(1);
        let (shutdown_complete_tx, mut shutdown_complete_rx) = mpsc::channel(1);
        
        // Create connection semaphore
        let limit_connections = Arc::new(Semaphore::new(self.config.max_connections));
        
        // Clone necessary data for the accept loop
        let thread_pools = self.thread_pools.clone();
        let logic_tx_clone = logic_tx.clone();
        let notify_shutdown_clone = notify_shutdown.clone();
        let shutdown_complete_tx_clone = shutdown_complete_tx.clone();
        let next_connection_id = Arc::new(self.next_connection_id);
        
        // Spawn the accept loop on IO runtime
        let accept_handle = thread_pools.spawn_io(async move {
            Self::accept_loop(
                listener,
                thread_pools,
                logic_tx_clone,
                notify_shutdown_clone,
                shutdown_complete_tx_clone,
                limit_connections,
                next_connection_id,
            ).await
        });
        
        // Wait for shutdown signal
        tokio::select! {
            result = accept_handle => {
                if let Err(e) = result {
                    error!("Accept loop failed: {:?}", e);
                }
            }
            _ = shutdown => {
                info!("Shutdown signal received");
            }
        }
        
        // Initiate graceful shutdown
        info!("Starting graceful shutdown");
        
        // Signal all connections to shutdown
        drop(notify_shutdown);
        
        // Send shutdown to logic thread
        let _ = logic_tx.send(LogicMessage::Shutdown).await;
        
        // Drop the final shutdown complete sender
        drop(shutdown_complete_tx);
        
        // Wait for all connections to finish
        let _ = shutdown_complete_rx.recv().await;
        
        // Wait for logic thread to finish
        let _ = logic_handle.await;
        
        info!("Graceful shutdown completed");
        Ok(())
    }
    
    /// Accept loop running on IO thread pool
    async fn accept_loop(
        mut listener: TcpListener,
        thread_pools: Arc<ThreadPoolManager>,
        logic_tx: mpsc::Sender<LogicMessage>,
        notify_shutdown: broadcast::Sender<()>,
        shutdown_complete_tx: mpsc::Sender<()>,
        limit_connections: Arc<Semaphore>,
        next_connection_id: Arc<AtomicU64>,
    ) -> crate::Result<()> {
        info!("Accept loop started on IO thread pool");
        
        loop {
            // Wait for connection permit
            let permit = limit_connections
                .clone()
                .acquire_owned()
                .await
                .map_err(|_| "Failed to acquire connection permit")?;
            
            // Accept new connection
            let socket = match Self::accept_with_backoff(&mut listener).await {
                Ok(socket) => socket,
                Err(e) => {
                    error!("Failed to accept connection: {}", e);
                    return Err(e);
                }
            };
            
            // Get connection ID
            let connection_id = next_connection_id.fetch_add(1, Ordering::Relaxed);
            
            // Create IO handler
            let io_handler = IoHandler::new(
                Connection::new(socket),
                logic_tx.clone(),
                notify_shutdown.subscribe(), // Placeholder for io_broadcast_rx
                Shutdown::new(notify_shutdown.subscribe()),
                connection_id,
            );
            
            // Spawn connection handler on IO thread pool
            let shutdown_complete_tx_clone = shutdown_complete_tx.clone();
            thread_pools.spawn_io(async move {
                let _permit = permit; // Move permit into task
                let _shutdown_complete = shutdown_complete_tx_clone; // Keep alive
                
                info!("Connection {} started", connection_id);
                
                // Run the IO handler
                let mut handler = io_handler;
                if let Err(e) = handler.run().await {
                    error!("Connection {} error: {}", connection_id, e);
                }
                
                info!("Connection {} finished", connection_id);
            });
        }
    }
    
    /// Accept with exponential backoff
    async fn accept_with_backoff(listener: &mut TcpListener) -> crate::Result<TcpStream> {
        let mut backoff = 1;
        
        loop {
            match listener.accept().await {
                Ok((socket, _)) => return Ok(socket),
                Err(err) => {
                    if backoff > 64 {
                        return Err(err.into());
                    }
                    
                    warn!("Accept failed, backing off for {}ms: {}", backoff, err);
                    time::sleep(Duration::from_millis(backoff)).await;
                    backoff *= 2;
                }
            }
        }
    }
    
    /// Set up CPU affinity for optimal performance
    fn setup_cpu_affinity() -> crate::Result<()> {
        let assignment = CpuAffinity::suggest_core_assignment();
        info!("Suggested CPU core assignment: {:?}", assignment);
        
        // Pin logic thread to dedicated core if available
        if let Some(logic_core) = assignment.logic_core {
            CpuAffinity::pin_logic_thread(logic_core)?;
        }
        
        Ok(())
    }
    
    /// Get server statistics
    pub fn stats(&self) -> ServerStats {
        ServerStats {
            thread_pools: self.thread_pools.stats(),
            max_connections: self.config.max_connections,
            current_connections: self.next_connection_id.load(Ordering::Relaxed) - 1,
        }
    }
}

/// Server statistics
#[derive(Debug, Clone)]
pub struct ServerStats {
    pub thread_pools: crate::thread_pools::ThreadPoolStats,
    pub max_connections: usize,
    pub current_connections: u64,
}

/// Run the optimized Redis server
pub async fn run_optimized(
    listener: TcpListener,
    shutdown: impl Future + Send + 'static,
    config: Option<ServerConfig>,
) -> crate::Result<()> {
    let config = config.unwrap_or_default();
    let server = OptimizedServer::new(config)?;
    server.run(listener, shutdown).await
}

/// Run with default configuration
pub async fn run_with_defaults(
    listener: TcpListener,
    shutdown: impl Future + Send + 'static,
) -> crate::Result<()> {
    run_optimized(listener, shutdown, None).await
}
