//! Thread pool management for Redis-like architecture
//!
//! This module provides dedicated thread pools for different types of work:
//! - Logic thread: Single dedicated thread for data operations
//! - IO thread pool: Multiple threads for network IO
//! - Background thread pool: For cleanup and maintenance tasks

use std::sync::Arc;
use tokio::runtime::{Builder, Runtime};
use tokio::sync::mpsc;
use tracing::{error, info};

/// Thread pool configuration
#[derive(Debug, Clone)]
pub struct ThreadPoolConfig {
    /// Number of IO worker threads
    pub io_threads: usize,
    /// Number of background worker threads  
    pub background_threads: usize,
    /// Enable thread-local optimization
    pub thread_local: bool,
}

impl Default for ThreadPoolConfig {
    fn default() -> Self {
        let cpu_count = num_cpus::get();
        Self {
            // Reserve 1 core for logic thread, rest for IO
            io_threads: (cpu_count - 1).max(1),
            background_threads: 2,
            thread_local: true,
        }
    }
}

/// Manages all thread pools for the Redis server
pub struct ThreadPoolManager {
    /// Dedicated single-threaded runtime for logic operations
    logic_runtime: Runtime,
    
    /// Multi-threaded runtime for IO operations
    io_runtime: Runtime,
    
    /// Multi-threaded runtime for background tasks
    background_runtime: Runtime,
    
    /// Configuration
    config: ThreadPoolConfig,
}

impl ThreadPoolManager {
    /// Create a new thread pool manager
    pub fn new(config: ThreadPoolConfig) -> crate::Result<Self> {
        info!("Creating thread pools with config: {:?}", config);
        
        // Create single-threaded runtime for logic operations
        let logic_runtime = Builder::new_current_thread()
            .thread_name("redis-logic")
            .enable_all()
            .build()
            .map_err(|e| format!("Failed to create logic runtime: {}", e))?;
        
        // Create multi-threaded runtime for IO operations
        let io_runtime = Builder::new_multi_thread()
            .worker_threads(config.io_threads)
            .thread_name("redis-io")
            .enable_all()
            .build()
            .map_err(|e| format!("Failed to create IO runtime: {}", e))?;
        
        // Create multi-threaded runtime for background tasks
        let background_runtime = Builder::new_multi_thread()
            .worker_threads(config.background_threads)
            .thread_name("redis-bg")
            .enable_all()
            .build()
            .map_err(|e| format!("Failed to create background runtime: {}", e))?;
        
        Ok(Self {
            logic_runtime,
            io_runtime,
            background_runtime,
            config,
        })
    }
    
    /// Spawn a task on the logic thread
    pub fn spawn_logic<F>(&self, future: F) -> tokio::task::JoinHandle<F::Output>
    where
        F: std::future::Future + Send + 'static,
        F::Output: Send + 'static,
    {
        self.logic_runtime.spawn(future)
    }
    
    /// Spawn a task on the IO thread pool
    pub fn spawn_io<F>(&self, future: F) -> tokio::task::JoinHandle<F::Output>
    where
        F: std::future::Future + Send + 'static,
        F::Output: Send + 'static,
    {
        self.io_runtime.spawn(future)
    }
    
    /// Spawn a task on the background thread pool
    pub fn spawn_background<F>(&self, future: F) -> tokio::task::JoinHandle<F::Output>
    where
        F: std::future::Future + Send + 'static,
        F::Output: Send + 'static,
    {
        self.background_runtime.spawn(future)
    }
    
    /// Get a handle to the logic runtime for blocking operations
    pub fn logic_handle(&self) -> &Runtime {
        &self.logic_runtime
    }
    
    /// Get a handle to the IO runtime
    pub fn io_handle(&self) -> &Runtime {
        &self.io_runtime
    }
    
    /// Get a handle to the background runtime
    pub fn background_handle(&self) -> &Runtime {
        &self.background_runtime
    }
    
    /// Get thread pool statistics
    pub fn stats(&self) -> ThreadPoolStats {
        ThreadPoolStats {
            logic_threads: 1,
            io_threads: self.config.io_threads,
            background_threads: self.config.background_threads,
        }
    }
    
    /// Shutdown all thread pools gracefully
    pub fn shutdown(self) {
        info!("Shutting down thread pools");
        
        // Shutdown in reverse order of importance
        self.background_runtime.shutdown_background();
        self.io_runtime.shutdown_background();
        self.logic_runtime.shutdown_background();
        
        info!("All thread pools shut down");
    }
}

/// Thread pool statistics
#[derive(Debug, Clone)]
pub struct ThreadPoolStats {
    pub logic_threads: usize,
    pub io_threads: usize,
    pub background_threads: usize,
}

/// CPU affinity management for better performance
pub struct CpuAffinity;

impl CpuAffinity {
    /// Set CPU affinity for the logic thread to a specific core
    pub fn pin_logic_thread(core_id: usize) -> crate::Result<()> {
        #[cfg(target_os = "linux")]
        {
            use core_affinity::{CoreId, set_for_current};
            if let Some(core) = CoreId { id: core_id } {
                if set_for_current(core) {
                    info!("Pinned logic thread to CPU core {}", core_id);
                    return Ok(());
                }
            }
            Err("Failed to set CPU affinity for logic thread".into())
        }
        
        #[cfg(not(target_os = "linux"))]
        {
            info!("CPU affinity not supported on this platform");
            Ok(())
        }
    }
    
    /// Get available CPU cores
    pub fn available_cores() -> Vec<usize> {
        (0..num_cpus::get()).collect()
    }
    
    /// Suggest optimal core assignment
    pub fn suggest_core_assignment() -> CoreAssignment {
        let cores = Self::available_cores();
        let total_cores = cores.len();
        
        if total_cores == 1 {
            CoreAssignment {
                logic_core: Some(0),
                io_cores: vec![0],
                background_cores: vec![0],
            }
        } else if total_cores == 2 {
            CoreAssignment {
                logic_core: Some(0),
                io_cores: vec![1],
                background_cores: vec![1],
            }
        } else {
            // Reserve first core for logic, rest for IO and background
            CoreAssignment {
                logic_core: Some(0),
                io_cores: (1..total_cores-1).collect(),
                background_cores: vec![total_cores-1],
            }
        }
    }
}

/// CPU core assignment recommendation
#[derive(Debug, Clone)]
pub struct CoreAssignment {
    pub logic_core: Option<usize>,
    pub io_cores: Vec<usize>,
    pub background_cores: Vec<usize>,
}

/// Thread-local storage for performance optimization
pub struct ThreadLocalStorage {
    /// Per-thread connection buffers
    connection_buffers: thread_local::ThreadLocal<Vec<u8>>,
    
    /// Per-thread frame parsing buffers
    frame_buffers: thread_local::ThreadLocal<Vec<u8>>,
}

impl ThreadLocalStorage {
    pub fn new() -> Self {
        Self {
            connection_buffers: thread_local::ThreadLocal::new(),
            frame_buffers: thread_local::ThreadLocal::new(),
        }
    }
    
    /// Get or create a connection buffer for the current thread
    pub fn get_connection_buffer(&self) -> &mut Vec<u8> {
        self.connection_buffers.get_or(|| Vec::with_capacity(4096))
    }
    
    /// Get or create a frame buffer for the current thread
    pub fn get_frame_buffer(&self) -> &mut Vec<u8> {
        self.frame_buffers.get_or(|| Vec::with_capacity(1024))
    }
}

/// Performance monitoring for thread pools
pub struct ThreadPoolMonitor {
    stats_tx: mpsc::Sender<ThreadPoolStats>,
}

impl ThreadPoolMonitor {
    pub fn new() -> (Self, mpsc::Receiver<ThreadPoolStats>) {
        let (stats_tx, stats_rx) = mpsc::channel(100);
        (Self { stats_tx }, stats_rx)
    }
    
    /// Start monitoring thread pool performance
    pub async fn start_monitoring(&self, manager: Arc<ThreadPoolManager>) {
        let mut interval = tokio::time::interval(std::time::Duration::from_secs(10));
        
        loop {
            interval.tick().await;
            
            let stats = manager.stats();
            if let Err(e) = self.stats_tx.send(stats).await {
                error!("Failed to send thread pool stats: {}", e);
                break;
            }
        }
    }
}
