//! New Redis-like server implementation with IO threads + single logic thread
//!
//! This module implements the new architecture where:
//! - Multiple IO threads handle network connections
//! - A single logic thread handles all data operations
//! - No locks needed in the logic thread

use crate::engine::{EngineFactory, LogicEngine, LogicMessage};
use crate::io_handler::IoHandler;
use crate::{Connection, Shutdown};

use std::future::Future;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::{broadcast, mpsc, Semaphore};
use tokio::time::{self, Duration};
use tracing::{debug, error, info, instrument, warn};

/// Maximum number of concurrent connections
const MAX_CONNECTIONS: usize = 250;

/// Server listener state for the new architecture
#[derive(Debug)]
struct Listener {
    /// TCP listener supplied by the `run` caller
    listener: TcpListener,

    /// Channel to send commands to logic thread
    logic_tx: mpsc::Sender<LogicMessage>,

    /// Broadcasts a shutdown signal to all active connections
    notify_shutdown: broadcast::Sender<()>,

    /// Used as part of the graceful shutdown process to wait for client
    /// connections to complete processing.
    shutdown_complete_rx: mpsc::Receiver<()>,
    shutdown_complete_tx: mpsc::Sender<()>,

    /// Limit the max number of connections.
    limit_connections: Arc<Semaphore>,

    /// Connection ID counter
    next_connection_id: Arc<AtomicU64>,
}

/// Per-connection handler state for the new architecture
#[derive(Debug)]
struct Handler {
    /// IO handler for this connection
    io_handler: IoHandler,

    /// Notifies the receiver half once all clones are dropped.
    _shutdown_complete: mpsc::Sender<()>,
}

/// Run the mini-redis server with the new architecture.
///
/// Accept connections from the supplied listener. For each inbound connection,
/// spawn a task to handle that connection. The server runs until the `shutdown`
/// future completes, at which point the server shuts down gracefully.
///
/// `tokio::signal::ctrl_c()` can be used as the `shutdown` argument. This will
/// listen for a SIGINT signal.
pub async fn run(listener: TcpListener, shutdown: impl Future) -> crate::Result<()> {
    // Create the logic engine and communication channels
    let (logic_engine, logic_tx, _io_broadcast_rx) = EngineFactory::create();

    // Start the logic thread
    let logic_handle = tokio::spawn(async move {
        logic_engine.run().await;
    });

    // When the provided `shutdown` future completes, we must send a shutdown
    // message to all active connections. We use a broadcast channel for this
    // purpose. The call below ignores the receiver of the broadcast pair, and when
    // a receiver is needed, the subscribe() method on the sender is used to create
    // one.
    let (notify_shutdown, _) = broadcast::channel(1);
    let (shutdown_complete_tx, shutdown_complete_rx) = mpsc::channel(1);

    // Initialize the listener state
    let mut server = Listener {
        listener,
        logic_tx: logic_tx.clone(),
        notify_shutdown: notify_shutdown.clone(),
        shutdown_complete_tx,
        shutdown_complete_rx,
        limit_connections: Arc::new(Semaphore::new(MAX_CONNECTIONS)),
        next_connection_id: Arc::new(AtomicU64::new(1)),
    };

    // Concurrently run the server and listen for the `shutdown` signal. The
    // server task runs until an error is encountered, so under normal
    // circumstances, this `select!` statement runs until the `shutdown` signal
    // is received.
    tokio::select! {
        res = server.run() => {
            // If an error is received here, accepting connections from the TCP
            // listener failed multiple times and the server is giving up and
            // shutting down.
            //
            // Errors encountered when handling individual connections do not
            // bubble up to this point.
            if let Err(err) = res {
                error!(cause = %err, "failed to accept");
            }
        }
        _ = shutdown => {
            // The shutdown signal has been received.
            info!("shutting down");
        }
    }

    // Extract the `shutdown_complete` receiver and transmitter
    // explicitly drop `shutdown_transmitter`. This is important, as the
    // `.await` below would otherwise never complete.
    let Listener {
        mut shutdown_complete_rx,
        shutdown_complete_tx,
        notify_shutdown,
        logic_tx,
        ..
    } = server;

    // When `notify_shutdown` is dropped, all tasks which have `subscribe`d will
    // receive the shutdown signal and can exit
    drop(notify_shutdown);

    // Send shutdown signal to logic thread
    let _ = logic_tx.send(LogicMessage::Shutdown).await;

    // Drop final `Sender` so the `Receiver` below can complete
    drop(shutdown_complete_tx);

    // Wait for all active connections to finish processing. As the `Sender`
    // handle held by the listener has been dropped above, the only remaining
    // `Sender` instances are held by connection handler tasks. When those drop,
    // the `mpsc` channel will close and `recv()` will return `None`.
    let _ = shutdown_complete_rx.recv().await;

    // Wait for logic thread to finish
    let _ = logic_handle.await;

    Ok(())
}

impl Listener {
    /// Run the server
    ///
    /// Listen for inbound connections. For each inbound connection, spawn a
    /// task to handle that connection.
    ///
    /// # Errors
    ///
    /// Returns `Err` if accepting returns an error. This can happen for a
    /// number reasons that resolve over time. For example, if the underlying
    /// operating system has reached an internal limit for max number of
    /// sockets, accept will fail.
    ///
    /// The process is not able to detect when a transient error resolves
    /// itself. One strategy for handling this is to implement a back off
    /// strategy, which is what we do here.
    async fn run(&mut self) -> crate::Result<()> {
        info!("accepting inbound connections");

        loop {
            // Wait for a permit to become available
            //
            // `acquire_owned` returns a permit that is bound to the semaphore.
            // When the permit value is dropped, it is automatically returned
            // to the semaphore. This is convenient in many cases.
            // Unfortunately, waiting for the permit to be acquired and then
            // using it in another async function would require the permit to
            // cross an async function boundary. This is **not** currently
            // supported by the borrow checker.
            //
            // Instead, `acquire_owned` is used to receive a permit with a
            // `'static` lifetime that can be moved across async function
            // boundaries.
            //
            // A second option would be to structure the code such that the
            // permit is acquired in the same function that needs to use it.
            // In our case, this would mean putting the "accept inbound
            // connection" logic into the task we spawn. That is a reasonable
            // structure, but the one we use here is more explicit about the
            // scope of the semaphore permit.
            let permit = self
                .limit_connections
                .clone()
                .acquire_owned()
                .await
                .unwrap();

            // Accept a new socket. This will attempt to perform error handling.
            // The `accept` method internally attempts to recover errors, so an
            // error here is non-recoverable.
            let socket = self.accept().await?;

            // Get connection ID
            let connection_id = self.next_connection_id.fetch_add(1, Ordering::Relaxed);

            // Create the necessary per-connection handler state.
            let mut handler = Handler {
                // Create IO handler for this connection
                io_handler: IoHandler::new(
                    Connection::new(socket),
                    self.logic_tx.clone(),
                    self.notify_shutdown.subscribe(), // This will be used for io_broadcast_rx
                    Shutdown::new(self.notify_shutdown.subscribe()),
                    connection_id,
                ),

                // Notifies the receiver half once all clones are dropped.
                _shutdown_complete: self.shutdown_complete_tx.clone(),
            };

            // Spawn a new task to process the connections. Tokio tasks are like
            // asynchronous green threads and are executed concurrently.
            tokio::spawn(async move {
                // Process the connection. If an error is encountered, log it.
                if let Err(err) = handler.run().await {
                    error!(cause = ?err, "connection error");
                }
                // Move the permit into the task and drop it after completion.
                // This returns the permit back to the semaphore.
                drop(permit);
            });
        }
    }

    /// Accept an inbound connection.
    ///
    /// Errors are handled by backing off and retrying. An acceptable backoff
    /// strategy is to exponentially backoff, starting with a small delay and
    /// increasing. This implementation is a simplified version of the
    /// strategy used in the standard library.
    ///
    /// The strategy is to start with a delay of 1 ms and double it each time.
    /// The delay is capped at 1 second. After the first failure, the delay
    /// will be 1 ms, then 2 ms, 4 ms, 8 ms, ... up to 1 second. Once the
    /// delay reaches 1 second, it stays at 1 second.
    async fn accept(&mut self) -> crate::Result<TcpStream> {
        let mut backoff = 1;

        // Try to accept a few times
        loop {
            // Perform the accept operation. If a socket is successfully
            // accepted, return it. Otherwise, save the error.
            match self.listener.accept().await {
                Ok((socket, _)) => return Ok(socket),
                Err(err) => {
                    if backoff > 64 {
                        // Accept has failed too many times. Return the error.
                        return Err(err.into());
                    }
                }
            }

            // Pause execution until the back off period elapses.
            time::sleep(Duration::from_millis(backoff)).await;

            // Double the back off
            backoff *= 2;
        }
    }
}

impl Handler {
    /// Process a single connection.
    ///
    /// Request frames are read from the socket and processed. Responses are
    /// written back to the socket.
    ///
    /// Currently, pipelining is not implemented. Pipelining is the ability to
    /// process more than one request concurrently per connection without
    /// interleaving frames. See for more details:
    /// https://redis.io/topics/pipelining
    ///
    /// When the shutdown signal is received, the connection is processed until
    /// it reaches a safe state, at which point it is terminated.
    #[instrument(skip(self))]
    async fn run(&mut self) -> crate::Result<()> {
        // Run the IO handler
        self.io_handler.run().await
    }
}
