//! IO thread handler for network connections
//!
//! This module handles individual client connections, focusing only on:
//! - Network IO (reading/writing frames)
//! - Protocol parsing
//! - Message passing to/from logic thread

use crate::engine::{IoMessage, LogicMessage};
use crate::{Command, Connection, Frame, Shutdown};
use bytes::Bytes;
use std::collections::HashMap;
use tokio::sync::{broadcast, mpsc, oneshot};
use tracing::{debug, error, info, instrument};

/// Handles a single client connection
pub struct IoHandler {
    /// The TCP connection for reading/writing frames
    connection: Connection,
    
    /// Channel to send commands to logic thread
    logic_tx: mpsc::Sender<LogicMessage>,
    
    /// Receiver for broadcast messages from logic thread
    io_broadcast_rx: broadcast::Receiver<IoMessage>,
    
    /// Shutdown signal
    shutdown: Shutdown,
    
    /// Active subscriptions for this connection
    /// Maps channel name to subscriber info
    subscriptions: HashMap<String, SubscriptionInfo>,
    
    /// Unique connection ID for debugging
    connection_id: u64,
}

/// Information about a subscription
#[derive(Debug)]
struct SubscriptionInfo {
    /// Subscriber ID assigned by logic thread
    subscriber_id: u64,
    /// Channel to receive published messages
    message_rx: mpsc::Receiver<Bytes>,
}

impl IoHandler {
    /// Create a new IO handler
    pub fn new(
        connection: Connection,
        logic_tx: mpsc::Sender<LogicMessage>,
        io_broadcast_rx: broadcast::Receiver<IoMessage>,
        shutdown: Shutdown,
        connection_id: u64,
    ) -> Self {
        Self {
            connection,
            logic_tx,
            io_broadcast_rx,
            shutdown,
            subscriptions: HashMap::new(),
            connection_id,
        }
    }
    
    /// Run the IO handler
    #[instrument(skip(self), fields(connection_id = %self.connection_id))]
    pub async fn run(&mut self) -> crate::Result<()> {
        info!("IO handler started");
        
        loop {
            tokio::select! {
                // Handle incoming frames from client
                frame_result = self.connection.read_frame() => {
                    match frame_result? {
                        Some(frame) => {
                            if let Err(e) = self.handle_frame(frame).await {
                                error!("Error handling frame: {}", e);
                                break;
                            }
                        }
                        None => {
                            debug!("Client disconnected");
                            break;
                        }
                    }
                }
                
                // Handle published messages for subscriptions
                message = self.receive_subscription_message() => {
                    if let Some((channel, message)) = message {
                        if let Err(e) = self.send_published_message(&channel, &message).await {
                            error!("Error sending published message: {}", e);
                            break;
                        }
                    }
                }
                
                // Handle shutdown signal
                _ = self.shutdown.recv() => {
                    info!("Received shutdown signal");
                    break;
                }
            }
        }
        
        // Clean up subscriptions
        self.cleanup_subscriptions().await;
        
        info!("IO handler finished");
        Ok(())
    }
    
    /// Handle a frame received from the client
    async fn handle_frame(&mut self, frame: Frame) -> crate::Result<()> {
        debug!("Received frame: {:?}", frame);
        
        // Parse the frame into a command
        let cmd = Command::from_frame(frame)?;
        
        match cmd {
            Command::Subscribe(subscribe) => {
                self.handle_subscribe_command(subscribe).await?;
            }
            Command::Unsubscribe(unsubscribe) => {
                self.handle_unsubscribe_command(unsubscribe).await?;
            }
            Command::Publish(publish) => {
                self.handle_publish_command(publish).await?;
            }
            _ => {
                // Regular commands (GET, SET, PING, etc.)
                self.handle_regular_command(cmd).await?;
            }
        }
        
        Ok(())
    }
    
    /// Handle regular commands by forwarding to logic thread
    async fn handle_regular_command(&mut self, cmd: Command) -> crate::Result<()> {
        let (response_tx, response_rx) = oneshot::channel();
        
        let message = LogicMessage::Command {
            cmd,
            response_tx,
        };
        
        // Send to logic thread
        if let Err(_) = self.logic_tx.send(message).await {
            return Err("Logic thread disconnected".into());
        }
        
        // Wait for response
        match response_rx.await {
            Ok(response) => {
                self.connection.write_frame(&response).await?;
            }
            Err(_) => {
                return Err("Failed to receive response from logic thread".into());
            }
        }
        
        Ok(())
    }
    
    /// Handle subscribe command
    async fn handle_subscribe_command(&mut self, subscribe: crate::cmd::Subscribe) -> crate::Result<()> {
        for channel in &subscribe.channels {
            // Create a channel for receiving published messages
            let (message_tx, message_rx) = mpsc::channel(100);
            
            let (response_tx, response_rx) = oneshot::channel();
            
            let message = LogicMessage::Subscribe {
                channel: channel.clone(),
                subscriber_tx: message_tx,
                response_tx,
            };
            
            // Send to logic thread
            if let Err(_) = self.logic_tx.send(message).await {
                return Err("Logic thread disconnected".into());
            }
            
            // Wait for response
            match response_rx.await {
                Ok(response) => {
                    // Extract subscriber ID from response
                    let subscriber_id = if let Frame::Array(ref arr) = response {
                        if arr.len() >= 3 {
                            if let Frame::Integer(id) = **arr.get(2).unwrap() {
                                id
                            } else {
                                0
                            }
                        } else {
                            0
                        }
                    } else {
                        0
                    };
                    
                    // Store subscription info
                    self.subscriptions.insert(channel.clone(), SubscriptionInfo {
                        subscriber_id,
                        message_rx,
                    });
                    
                    // Send response to client
                    self.connection.write_frame(&response).await?;
                }
                Err(_) => {
                    return Err("Failed to receive response from logic thread".into());
                }
            }
        }
        
        Ok(())
    }
    
    /// Handle unsubscribe command
    async fn handle_unsubscribe_command(&mut self, unsubscribe: crate::cmd::Unsubscribe) -> crate::Result<()> {
        let channels = if unsubscribe.channels.is_empty() {
            // Unsubscribe from all channels
            self.subscriptions.keys().cloned().collect()
        } else {
            unsubscribe.channels.clone()
        };
        
        for channel in channels {
            if let Some(subscription) = self.subscriptions.remove(&channel) {
                let (response_tx, response_rx) = oneshot::channel();
                
                let message = LogicMessage::Unsubscribe {
                    channel: channel.clone(),
                    subscriber_id: subscription.subscriber_id,
                    response_tx,
                };
                
                // Send to logic thread
                if let Err(_) = self.logic_tx.send(message).await {
                    return Err("Logic thread disconnected".into());
                }
                
                // Wait for response and send to client
                if let Ok(response) = response_rx.await {
                    self.connection.write_frame(&response).await?;
                }
            }
        }
        
        Ok(())
    }
    
    /// Handle publish command
    async fn handle_publish_command(&mut self, publish: crate::cmd::Publish) -> crate::Result<()> {
        let (response_tx, response_rx) = oneshot::channel();
        
        let message = LogicMessage::Publish {
            channel: publish.channel.clone(),
            message: publish.message.clone(),
            response_tx,
        };
        
        // Send to logic thread
        if let Err(_) = self.logic_tx.send(message).await {
            return Err("Logic thread disconnected".into());
        }
        
        // Wait for response
        match response_rx.await {
            Ok(response) => {
                self.connection.write_frame(&response).await?;
            }
            Err(_) => {
                return Err("Failed to receive response from logic thread".into());
            }
        }
        
        Ok(())
    }
    
    /// Receive a message from any active subscription
    async fn receive_subscription_message(&mut self) -> Option<(String, Bytes)> {
        // Check all subscription channels for messages
        for (channel, subscription) in &mut self.subscriptions {
            match subscription.message_rx.try_recv() {
                Ok(message) => {
                    return Some((channel.clone(), message));
                }
                Err(mpsc::error::TryRecvError::Empty) => {
                    // No message available, continue
                }
                Err(mpsc::error::TryRecvError::Disconnected) => {
                    // Channel closed, will be cleaned up later
                }
            }
        }
        None
    }
    
    /// Send a published message to the client
    async fn send_published_message(&mut self, channel: &str, message: &Bytes) -> crate::Result<()> {
        let mut response = Frame::Array(vec![]);
        if let Frame::Array(ref mut arr) = response {
            arr.push(Box::new(Frame::Bulk(Bytes::from_static(b"message"))));
            arr.push(Box::new(Frame::Bulk(Bytes::from(channel))));
            arr.push(Box::new(Frame::Bulk(message.clone())));
        }
        
        self.connection.write_frame(&response).await?;
        Ok(())
    }
    
    /// Clean up all subscriptions when connection closes
    async fn cleanup_subscriptions(&mut self) {
        for (channel, subscription) in self.subscriptions.drain() {
            let (response_tx, _) = oneshot::channel();
            
            let message = LogicMessage::Unsubscribe {
                channel,
                subscriber_id: subscription.subscriber_id,
                response_tx,
            };
            
            // Best effort cleanup - ignore errors
            let _ = self.logic_tx.send(message).await;
        }
    }
}
