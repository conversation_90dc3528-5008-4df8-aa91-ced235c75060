//! Optimized Mini-Redis server with dedicated thread pools
//!
//! This server uses:
//! - Dedicated single thread for logic operations (no contention)
//! - Separate IO thread pool for network operations  
//! - Background thread pool for maintenance tasks
//! - CPU affinity for optimal performance

use mini_redis::{server_optimized, thread_pools, DEFAULT_PORT};
use mini_redis::server_optimized::ServerConfig;
use mini_redis::thread_pools::ThreadPoolConfig;

use clap::Parser;
use tokio::net::TcpListener;
use tokio::signal;
use tracing::{error, info, warn};
use tracing_subscriber;

#[derive(Parser, Debug)]
#[clap(name = "mini-redis-server-optimized", version, author, about = "An optimized Redis server")]
struct Cli {
    /// Port to listen on
    #[clap(long, default_value_t = DEFAULT_PORT)]
    port: u16,
    
    /// Number of IO worker threads
    #[clap(long)]
    io_threads: Option<usize>,
    
    /// Number of background worker threads
    #[clap(long, default_value_t = 2)]
    background_threads: usize,
    
    /// Maximum concurrent connections
    #[clap(long, default_value_t = 1000)]
    max_connections: usize,
    
    /// Enable CPU affinity optimization
    #[clap(long)]
    enable_cpu_affinity: bool,
    
    /// Logic thread channel buffer size
    #[clap(long, default_value_t = 10000)]
    logic_channel_buffer: usize,
    
    /// IO broadcast channel buffer size
    #[clap(long, default_value_t = 1000)]
    io_broadcast_buffer: usize,
    
    /// Show thread pool information and exit
    #[clap(long)]
    show_thread_info: bool,
}

#[tokio::main]
pub async fn main() -> mini_redis::Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt::try_init().map_err(|_| "Failed to initialize tracing")?;

    let cli = Cli::parse();
    
    // Show thread pool information if requested
    if cli.show_thread_info {
        show_thread_info();
        return Ok(());
    }
    
    // Create thread pool configuration
    let thread_pool_config = ThreadPoolConfig {
        io_threads: cli.io_threads.unwrap_or_else(|| {
            let cpu_count = num_cpus::get();
            (cpu_count - 1).max(1) // Reserve 1 core for logic thread
        }),
        background_threads: cli.background_threads,
        thread_local: true,
    };
    
    // Create server configuration
    let server_config = ServerConfig {
        thread_pools: thread_pool_config,
        max_connections: cli.max_connections,
        enable_cpu_affinity: cli.enable_cpu_affinity,
        logic_channel_buffer: cli.logic_channel_buffer,
        io_broadcast_buffer: cli.io_broadcast_buffer,
    };
    
    info!("Server configuration: {:?}", server_config);
    
    // Bind TCP listener
    let listener = TcpListener::bind(&format!("127.0.0.1:{}", cli.port)).await?;
    info!("Optimized Mini-Redis server listening on port {}", cli.port);
    
    // Show CPU information
    show_cpu_info();
    
    // Run the optimized server
    server_optimized::run_optimized(listener, signal::ctrl_c(), Some(server_config)).await
}

fn show_thread_info() {
    let cpu_count = num_cpus::get();
    let assignment = thread_pools::CpuAffinity::suggest_core_assignment();
    
    println!("=== Thread Pool Information ===");
    println!("Available CPU cores: {}", cpu_count);
    println!("Suggested core assignment:");
    println!("  Logic thread: {:?}", assignment.logic_core);
    println!("  IO threads: {:?}", assignment.io_cores);
    println!("  Background threads: {:?}", assignment.background_cores);
    println!();
    println!("Default configuration:");
    println!("  IO threads: {}", (cpu_count - 1).max(1));
    println!("  Background threads: 2");
    println!("  Max connections: 1000");
    println!("  Logic channel buffer: 10000");
    println!("  IO broadcast buffer: 1000");
}

fn show_cpu_info() {
    let cpu_count = num_cpus::get();
    let assignment = thread_pools::CpuAffinity::suggest_core_assignment();
    
    info!("CPU Information:");
    info!("  Available cores: {}", cpu_count);
    info!("  Logic thread core: {:?}", assignment.logic_core);
    info!("  IO thread cores: {:?}", assignment.io_cores);
    info!("  Background thread cores: {:?}", assignment.background_cores);
    
    #[cfg(target_os = "linux")]
    info!("CPU affinity optimization available");
    
    #[cfg(not(target_os = "linux"))]
    warn!("CPU affinity optimization not available on this platform");
}
