//! Mini-Redis server with new architecture
//!
//! This is the main entry point for the new Redis-like server that uses:
//! - Multiple IO threads for handling connections
//! - Single logic thread for data operations (no locks)
//! - Message passing for communication

use mini_redis::{server_new, DEFAULT_PORT};

use clap::Parser;
use tokio::net::TcpListener;
use tokio::signal;
use tracing::{error, info, warn};
use tracing_subscriber;

#[derive(Parser, Debug)]
#[clap(name = "mini-redis-server-new", version, author, about = "A Redis server with new architecture")]
struct Cli {
    #[clap(long)]
    port: Option<u16>,
}

#[tokio::main]
pub async fn main() -> mini_redis::Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt::try_init().map_err(|_| "Failed to initialize tracing")?;

    let cli = Cli::parse();

    // Bind a TCP listener
    let port = cli.port.unwrap_or(DEFAULT_PORT);
    let listener = TcpListener::bind(&format!("127.0.0.1:{}", port)).await?;

    info!("Mini-Redis server (new architecture) listening on port {}", port);

    // Run the server with graceful shutdown
    server_new::run(listener, signal::ctrl_c()).await
}
